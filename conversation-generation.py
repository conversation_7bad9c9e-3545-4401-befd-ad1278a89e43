from openai import OpenAI
import json
import random
import os
import datetime

def get_response(model,messages):
    """
    获取对话生成的响应
    :param messages: 对话消息列表
    :return: 生成的响应文本
    """
    
    # 指定url和key
    api_key = "sk-RMerNTL9uP4lmHh_1rwUHJvcaqKXXzb7IrAtjWTa5Ln_aFMFTNPkuzIi7vw"
    base_url = "http://43.153.114.96:3000/v1"
    
    client = OpenAI(
    base_url=base_url,
    api_key=api_key)
    
    response = client.chat.completions.create(
        messages=json.loads(messages),
        model=model,  # 也可以选doubao
        stream=False
    )
    
    return response.choices[0].message.content

# 使用示例
if __name__ == "__main__":
    with open(r"/home/<USER>/Client-simulation/personas.json", 'r', encoding='utf-8') as file:
        personas = json.load(file)
    # 随机选择一个persona
    selected_persona = random.choice(personas) if personas else None
    
    # print("=== 随机选择的Persona ===")
    # print(selected_persona)
    
    client_prompt = '''
    你需要根据提供的角色卡片（一定有）和历史对话内容（如有）以心理咨询来访者的角度进行生成或续写，每次生成需体现以下核心原则：
    
    1. **人格沉浸**
    - 这一点是最关键的，以下的几个要点都需要不断参照这一点进行生成。
    - 角色卡片中包含"Gender", "Age", "Occupation", "Personality", "Emotional Experience Words", "Core Drive", "Reaction Pattern"字段，这些字段充分体现这些人格特质。在整段的对话的生成中，需要不断的依据这些特质，并将它们贯彻始终。
    - 其中"Gender", "Age", "Occupation"字段，作为客观的社会身份，会影响角色的表达方式、使用语句和思维方式。
    - "Personality", Emotional Experience Words", "Core Drive", "Reaction Pattern"字段，作为角色的性格特质，会影响角色的情感反应和表达；行为模式和底层逻辑。
    - 这两个大方向都需要被兼顾到，你必须先思考具有以上特质的角色会有怎样的表现方式，再根据思考结果生成应有内容。
    
    2. **对话内容约束**
    - 不要离开角色视角，不要点评或分析，仅以“我”的视角下本人的表达。
    - 在表达的同时，次要地需要适当的参考咨询师的发言，使得两个人的对话内容整体看来是合理的。 
    
    3. **初期事件清晰**  
    - 角色卡片中包含"Event Time", "Event Location", "Event Participants", "Event Description"等等字段，在现在处于初期1-5轮对话中，需要着重关注这些字段，围绕事件尝试进行表达。
    
    4. **中期对话发散** 
    - 卡片中包含"Topic", "Subtopic", "Situation", "Coped Strategies and Effects"字段，在中期对话中，如果已经将初期事件表达完整，尝试围绕这些字段进行发散性表达。
    - 例如思考"Topic", "Subtopic", "Situation"中相似事件的发展
    - 例如根据"Coped Strategies and Effects"字段，思考自己在事件中使用的应对策略和效果会导致事件的发展。
    
    5. **结尾事件控制
    - 关注到"Goals and Expectations"字段，在中后期与咨询师的对话中，尝试围绕这些目标和期望进行表达。
    - 如果能解决自己期望，或者不能解决期望时，都可以显示的表达出来。
    
    **角色卡片如下：**
    {persona}
    
    **历史对话内容如下（如有）：**
    {history}
    '''
    
    counselor_prompt = '''
    你是一名专业的心理咨询师，继续与有心理困扰的来访者对话。不要用markdown格式。你的回答保持简短，字数限制在100字左右。
    
    **历史对话内容如下：**
    {history}
    '''
    
    # 创建对话记录列表，每一项为一轮对话（包含来访者和咨询师的对话）
    conversation_history = []
    formatted_history_list = []  # 格式化后的历史记录，用于显示
    
    # 获取当前日期时间作为文件名
    current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"conversation_{current_time}.txt"
    
    # 获取persona的基本信息用于文件头部
    persona_info = f"角色卡片id: {selected_persona.get('persona_id', 'N/A')}, 性别: {selected_persona.get('Gender', 'N/A')}, 年龄: {selected_persona.get('Age', 'N/A')}, 职业: {selected_persona.get('Occupation', 'N/A')}"
    
    # 开始对话循环
    for i in range(10):
        print(f"===== 第 {i+1} 轮对话 =====")
        
        # 合并历史对话
        history_str = "\n".join(formatted_history_list)
        
        # 1. 生成来访者的回复
        formatted_client_prompt = client_prompt.format(
            persona=json.dumps(selected_persona, ensure_ascii=False, indent=2),
            history=history_str
        )
        
        client_messages = [
            {"role": "system", "content": "你是一名专业的心理咨询来访者模拟器，必须严格依据提供的角色卡片，完全扮演这位来访者；生成自然、真实、多样化的第一人称叙述。"},
            {"role": "user", "content": formatted_client_prompt},
        ]
        client_messages_json = json.dumps(client_messages, ensure_ascii=False)
        client_response = get_response("gpt-4.1", client_messages_json)
        
        # 将来访者回复添加到格式化历史记录
        formatted_history_list.append(f"来访者: {client_response}")
        print(f"来访者: {client_response}")
        
        # 2. 生成咨询师的回复
        formatted_counselor_prompt = counselor_prompt.format(
            history=history_str + f"\n来访者: {client_response}"
        )
        
        counselor_messages = [
            {"role": "system", "content": "你是一名专业的心理咨询师，与有心理困扰的来访者对话。不要用markdown格式。你的回答保持简短，字数限制在100字左右。"},
            {"role": "user", "content": formatted_counselor_prompt},
        ]
        counselor_messages_json = json.dumps(counselor_messages, ensure_ascii=False)
        counselor_response = get_response("deepseek-r1-250528", counselor_messages_json)
        
        # 将咨询师回复添加到格式化历史记录
        formatted_history_list.append(f"咨询师: {counselor_response}")
        print(f"咨询师: {counselor_response}")
        
        # 存储这一轮完整对话
        conversation_history.append({
            "round": i+1,
            "client": client_response,
            "counselor": counselor_response
        })
        
        print("\n")
    
    # 将对话保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        # 写入文件头部信息
        f.write(f"=== 心理咨询对话记录 ===\n")
        f.write(f"日期: {current_time.split('_')[0]}\n")
        f.write(f"来访者信息: {persona_info}\n")
        f.write(f"对话轮次: 20\n")
        f.write("\n" + "="*50 + "\n\n")
        
        # 写入对话内容
        for item in conversation_history:
            f.write(f"【第 {item['round']} 轮】\n")
            f.write(f"来访者: {item['client']}\n\n")
            f.write(f"咨询师: {item['counselor']}\n")
            f.write("-"*50 + "\n")
    
    print(f"对话已保存至文件: {output_file}")





