#!/usr/bin/env python3
"""
测试修改后的expand_fields.py的关键功能
"""

import json
import os
import datetime
import threading

def test_append_to_output_file():
    """测试实时追加功能"""
    test_file = "test_output.json"
    lock = threading.Lock()
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 测试数据
    test_data = [
        {"persona_id": "001", "name": "测试角色1", "processing_index": 0},
        {"persona_id": "002", "name": "测试角色2", "processing_index": 1},
        {"persona_id": "003", "name": "测试角色3", "processing_index": 2}
    ]
    
    # 模拟append_to_output_file函数
    def append_to_output_file(persona_data, output_file, lock=None):
        if lock:
            lock.acquire()
            
        try:
            # 检查文件是否存在，如果不存在则创建并写入开始的方括号
            if not os.path.exists(output_file):
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write('[\n')
            
            # 读取现有内容以确定是否需要添加逗号
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 如果文件只有开始的方括号，直接添加数据
            if content == '[':
                with open(output_file, 'a', encoding='utf-8') as f:
                    json.dump(persona_data, f, ensure_ascii=False, indent=2)
            else:
                # 移除最后的方括号，添加逗号和新数据
                with open(output_file, 'r+', encoding='utf-8') as f:
                    f.seek(0, 2)  # 移动到文件末尾
                    f.seek(f.tell() - 1)  # 回退一个字符
                    f.truncate()  # 删除最后的方括号
                    f.write(',\n')
                    json.dump(persona_data, f, ensure_ascii=False, indent=2)
            
            # 添加结束的方括号
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write('\n]')
            
            return True
        except Exception as e:
            print(f"实时保存到文件失败: {e}")
            return False
        finally:
            if lock:
                lock.release()
    
    # 测试追加功能
    print("测试实时追加功能...")
    for i, data in enumerate(test_data):
        success = append_to_output_file(data, test_file, lock)
        print(f"追加第{i+1}个数据: {'成功' if success else '失败'}")
    
    # 验证结果
    try:
        with open(test_file, 'r', encoding='utf-8') as f:
            result = json.load(f)
        print(f"最终文件包含 {len(result)} 个项目")
        print("文件内容验证:", "成功" if len(result) == len(test_data) else "失败")
        
        # 验证顺序
        order_correct = all(result[i]['processing_index'] == i for i in range(len(result)))
        print("顺序验证:", "正确" if order_correct else "错误")
        
    except Exception as e:
        print(f"验证文件失败: {e}")
    
    # 清理
    if os.path.exists(test_file):
        os.remove(test_file)

def test_error_file():
    """测试错误文件功能"""
    error_file = "test_errors.json"
    lock = threading.Lock()
    
    # 清理测试文件
    if os.path.exists(error_file):
        os.remove(error_file)
    
    def append_to_error_file(persona_id, error_info, error_file, lock=None):
        if lock:
            lock.acquire()
            
        try:
            error_data = {
                "persona_id": persona_id,
                "error_info": error_info,
                "timestamp": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 检查文件是否存在，如果不存在则创建并写入开始的方括号
            if not os.path.exists(error_file):
                with open(error_file, 'w', encoding='utf-8') as f:
                    f.write('[\n')
            
            # 读取现有内容以确定是否需要添加逗号
            with open(error_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 如果文件只有开始的方括号，直接添加数据
            if content == '[':
                with open(error_file, 'a', encoding='utf-8') as f:
                    json.dump(error_data, f, ensure_ascii=False, indent=2)
            else:
                # 移除最后的方括号，添加逗号和新数据
                with open(error_file, 'r+', encoding='utf-8') as f:
                    f.seek(0, 2)  # 移动到文件末尾
                    f.seek(f.tell() - 1)  # 回退一个字符
                    f.truncate()  # 删除最后的方括号
                    f.write(',\n')
                    json.dump(error_data, f, ensure_ascii=False, indent=2)
            
            # 添加结束的方括号
            with open(error_file, 'a', encoding='utf-8') as f:
                f.write('\n]')
            
            return True
        except Exception as e:
            print(f"保存错误信息到文件失败: {e}")
            return False
        finally:
            if lock:
                lock.release()
    
    # 测试错误记录
    print("\n测试错误记录功能...")
    test_errors = [
        ("001", "JSON解析失败"),
        ("002", "网络连接超时"),
        ("003", "模型响应异常")
    ]
    
    for persona_id, error_info in test_errors:
        success = append_to_error_file(persona_id, error_info, error_file, lock)
        print(f"记录错误 {persona_id}: {'成功' if success else '失败'}")
    
    # 验证错误文件
    try:
        with open(error_file, 'r', encoding='utf-8') as f:
            errors = json.load(f)
        print(f"错误文件包含 {len(errors)} 个错误记录")
        print("错误文件验证:", "成功" if len(errors) == len(test_errors) else "失败")
    except Exception as e:
        print(f"验证错误文件失败: {e}")
    
    # 清理
    if os.path.exists(error_file):
        os.remove(error_file)

if __name__ == "__main__":
    print("开始测试修改后的功能...")
    test_append_to_output_file()
    test_error_file()
    print("\n测试完成！")
