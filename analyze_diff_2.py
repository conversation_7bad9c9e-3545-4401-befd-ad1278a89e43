#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
import argparse
from collections import Counter
from typing import Any, Dict, List, Tuple
import jieba
import matplotlib.pyplot as plt
from wordcloud import WordCloud
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，避免显示问题

# 设置默认字体路径
DEFAULT_FONT_PATH = '/home/<USER>/Client-simulation/Ubuntu_18.04_SimHei.ttf'

# 配置matplotlib使用中文字体
if os.path.exists(DEFAULT_FONT_PATH):
    matplotlib.font_manager.fontManager.addfont(DEFAULT_FONT_PATH)
    plt.rcParams['font.family'] = 'SimHei'
    plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

# 通用停用词列表，用于所有分析
COMMON_STOPWORDS = {'觉得', '比如', '它们', '这次', '事件', '的', '了', '和', '是', '在', '我', '有', '与', '这', '他', '你', '但', '，', '。', '；', '：', '"', '"', '？', '！', 
                '、', '对', '中', '从', '到', '为', '被', '用', '能', '可', '上', '下', '1', '2', '3', '4', '5', '以', '及', '等', '把', 
                '就', '也', '或', '而', '很', '会', '那', '都', '说', '着', '将', '让', '做', '去', '还', '得', '时', '年', '月', '日'}

def normalize_field_value(value: Any) -> str:
    """
    将各种类型的字段值标准化为字符串，用于文本分析
    
    Args:
        value: 任意类型的字段值（字符串、列表、字典等）
        
    Returns:
        str: 标准化后的字符串
    """
    if isinstance(value, str):
        return value
    elif isinstance(value, list):
        # 如果是列表，将所有元素转为字符串并用空格连接
        return " ".join(normalize_field_value(item) for item in value if item)
    elif isinstance(value, dict):
        # 如果是字典，将所有值转为字符串并用空格连接
        return " ".join(normalize_field_value(v) for v in value.values() if v)
    else:
        # 其他类型尝试转为字符串
        try:
            return str(value)
        except:
            return ""

def load_json_data(file_path: str) -> List[Dict]:
    """
    加载JSON文件数据
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        List[Dict]: JSON数据列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        if isinstance(data, dict):
            return [data]  # 单个对象转为列表
        return data
    except Exception as e:
        print(f"读取JSON文件失败: {e}")
        return []

def analyze_word_frequency(text: str, top_n: int = 30, additional_stopwords: set = None) -> List[Tuple[str, int]]:
    """
    分析文本词频
    
    Args:
        text: 要分析的文本
        top_n: 返回前N个高频词
        additional_stopwords: 额外的停用词集合
        
    Returns:
        List[Tuple[str, int]]: 词频统计结果，格式为 [(词1, 频次1), (词2, 频次2), ...]
    """
    # 使用jieba分词
    words = jieba.cut(text)
    
    # 合并停用词
    stopwords = COMMON_STOPWORDS.copy()
    if additional_stopwords:
        stopwords.update(additional_stopwords)
    
    # 过滤停用词和标点符号
    filtered_words = [word for word in words if word.strip() and word not in stopwords and len(word) > 1]
    
    # 统计词频
    word_counts = Counter(filtered_words)
    
    # 返回前N个高频词
    return word_counts.most_common(top_n)

def generate_wordcloud(text: str, output_path: str, title: str = "词云图", additional_stopwords: set = None, font_path: str = DEFAULT_FONT_PATH):
    """
    生成词云图并保存
    
    Args:
        text: 文本内容
        output_path: 输出文件路径
        title: 词云图标题
        additional_stopwords: 额外的停用词集合
        font_path: 字体文件路径
    """
    # 使用jieba分词
    words = jieba.cut(text)
    
    # 合并停用词
    stopwords = COMMON_STOPWORDS.copy()
    if additional_stopwords:
        stopwords.update(additional_stopwords)
    
    # 过滤停用词
    word_space_split = " ".join([word for word in words if word not in stopwords and len(word) > 1])
    
    # 配置词云
    if os.path.exists(font_path):
        wordcloud = WordCloud(font_path=font_path,  # 使用指定的中文字体文件
                            width=800, 
                            height=400,
                            background_color='white',
                            max_words=100,
                            max_font_size=150, 
                            random_state=42).generate(word_space_split)
    else:
        print(f"警告：找不到字体文件 {font_path}，可能导致中文显示异常")
        wordcloud = WordCloud(width=800, 
                            height=400,
                            background_color='white',
                            max_words=100,
                            max_font_size=150, 
                            random_state=42).generate(word_space_split)
    
    # 绘制词云图
    plt.figure(figsize=(10, 5))
    plt.imshow(wordcloud, interpolation='bilinear')
    plt.axis('off')
    plt.title(title)
    plt.tight_layout()
    
    # 保存图片
    plt.savefig(output_path)
    plt.close()
    
    print(f"词云图已保存至: {output_path}")

def extract_supporter_roles(text: str, top_n: int = 30) -> List[Tuple[str, int]]:
    """
    从文本中提取支持者角色并统计频次
    
    Args:
        text: 包含支持者角色的文本
        top_n: 返回前N个高频角色
        
    Returns:
        List[Tuple[str, int]]: 角色频次统计结果
    """
    # 支持者角色相关词汇（可以根据实际数据进一步扩充）
    supporter_roles = {
        '亲属': ['父亲', '母亲', '爸爸', '妈妈', '哥哥', '姐姐', '弟弟', '妹妹', '爷爷', '奶奶', '外公', '外婆', 
                '叔叔', '阿姨', '舅舅', '舅妈', '姑姑', '姑父', '表哥', '表姐', '表弟', '表妹', '堂哥', '堂姐', 
                '堂弟', '堂妹', '儿子', '女儿', '孙子', '孙女'],
        '学校': ['老师', '同学', '班主任', '校长', '辅导员', '导师', '教授', '助教', '班长', '学生'],
        '职场': ['同事', '上司', '领导', '老板', '经理', '主管', '部门', '团队', '员工', '下属', '客户'],
        '朋友': ['朋友', '好友', '密友', '挚友', '知己', '闺蜜', '兄弟', '姐妹', '伙伴'],
        '社交': ['网友', '粉丝', '社区', '群组', '俱乐部', '邻居', '室友'],
        '专业人士': ['医生', '心理医生', '心理咨询师', '律师', '会计', '教练', '治疗师', '社工']
    }
    
    # 将支持者角色词汇扁平化为列表
    all_role_words = [role for category_roles in supporter_roles.values() for role in category_roles]
    
    # 加载自定义词典（确保jieba能正确分词这些角色词）
    for role in all_role_words:
        jieba.add_word(role)
    
    # 分词
    words = jieba.cut(text)
    
    # 筛选支持者角色词
    role_words = [word for word in words if any(role in word for role in all_role_words)]
    
    # 统计词频
    role_counts = Counter(role_words)
    
    # 返回前N个高频角色
    return role_counts.most_common(top_n)

def analyze_strengths_resources(data: List[Dict], font_path: str = DEFAULT_FONT_PATH) -> Dict:
    """
    分析StrengthsAndResources字段
    
    Args:
        data: JSON数据列表
        font_path: 字体文件路径
        
    Returns:
        Dict: 分析结果
    """
    # 提取所有StrengthsAndResources字段值
    all_strengths = []
    for item in data:
        if 'StrengthsAndResources' in item:
            all_strengths.extend(item['StrengthsAndResources'])
    
    # 统计项目数量
    count = len(all_strengths)
    
    # 将所有内容合并成一个文本字符串
    all_text = normalize_field_value(all_strengths)
    
    # 词频分析
    word_freq = analyze_word_frequency(all_text, 30)
    
    # 分析每项的平均字数
    avg_length = sum(len(s) for s in all_strengths) / count if count > 0 else 0
    
    # 生成词云
    output_dir = "wordcloud_output"
    os.makedirs(output_dir, exist_ok=True)
    generate_wordcloud(all_text, f"{output_dir}/strengths_resources_wordcloud.png", 
                       "优势与资源词云图", font_path=font_path)
    
    return {
        'total_items': count,
        'average_length': avg_length,
        'word_frequency': word_freq,
        'wordcloud_path': f"{output_dir}/strengths_resources_wordcloud.png"
    }

def analyze_social_support(data: List[Dict], font_path: str = DEFAULT_FONT_PATH) -> Dict:
    """
    分析SocialSupportSystem字段
    
    Args:
        data: JSON数据列表
        font_path: 字体文件路径
        
    Returns:
        Dict: 分析结果
    """
    # 提取所有SocialSupportSystem字段值
    all_supports = []
    for item in data:
        if 'SocialSupportSystem' in item:
            all_supports.append(item['SocialSupportSystem'])
    
    # 统计支持者类型（基于原始键）
    support_types = {}
    for support in all_supports:
        for key in support.keys():
            if key in support_types:
                support_types[key] += 1
            else:
                support_types[key] = 1
    
    # 将所有内容合并成一个文本字符串
    all_text = normalize_field_value(all_supports)
    
    # 词频分析
    word_freq = analyze_word_frequency(all_text, 30)
    
    # 提取支持者角色并统计
    supporter_roles = extract_supporter_roles(all_text)
    
    # 分析支持系统的规模（平均每个案例有多少支持者）
    avg_support_size = sum(len(s) for s in all_supports) / len(all_supports) if all_supports else 0
    
    # 生成词云
    output_dir = "wordcloud_output"
    os.makedirs(output_dir, exist_ok=True)
    generate_wordcloud(all_text, f"{output_dir}/social_support_wordcloud.png", 
                       "社会支持系统词云图", font_path=font_path)
    
    return {
        'total_systems': len(all_supports),
        'average_supporters': avg_support_size,
        'support_types': support_types,
        'supporter_roles': supporter_roles,  # 新增：基于分词的支持者角色统计
        'word_frequency': word_freq,
        'wordcloud_path': f"{output_dir}/social_support_wordcloud.png"
    }

def analyze_formative_experiences(data: List[Dict], font_path: str = DEFAULT_FONT_PATH) -> Dict:
    """
    分析FormativeExperiences字段
    
    Args:
        data: JSON数据列表
        font_path: 字体文件路径
        
    Returns:
        Dict: 分析结果
    """
    # 提取所有FormativeExperiences字段值
    all_experiences = []
    for item in data:
        if 'FormativeExperiences' in item:
            all_experiences.extend(item['FormativeExperiences'])
    
    # 统计经历数量
    count = len(all_experiences)
    
    # 分别提取事件和影响
    events = [exp.get('事件', '') for exp in all_experiences if isinstance(exp, dict)]
    impacts = [exp.get('影响', '') for exp in all_experiences if isinstance(exp, dict)]
    
    # 将所有内容合并成文本字符串
    all_text = normalize_field_value(all_experiences)
    events_text = ' '.join(events)
    impacts_text = ' '.join(impacts)
    
    # 词频分析
    word_freq = analyze_word_frequency(all_text, 30)
    events_word_freq = analyze_word_frequency(events_text, 20)
    impacts_word_freq = analyze_word_frequency(impacts_text, 20)
    
    # 分析年龄提及情况
    age_pattern = re.compile(r'(\d+)岁')
    ages = [int(match.group(1)) for match in age_pattern.finditer(all_text)]
    avg_age = sum(ages) / len(ages) if ages else None
    
    # 分析情感词汇
    emotion_words = {'积极': ['开心', '高兴', '快乐', '兴奋', '喜悦', '激动', '感动', '满足', '幸福'],
                    '消极': ['悲伤', '难过', '痛苦', '失望', '沮丧', '焦虑', '恐惧', '愤怒', '忧虑']}
    
    # 加载情感词汇到jieba
    for words in emotion_words.values():
        for word in words:
            jieba.add_word(word)
    
    # 统计情感词出现次数
    emotion_counts = {'积极': 0, '消极': 0}
    for word in jieba.cut(impacts_text):
        for emotion, words in emotion_words.items():
            if word in words:
                emotion_counts[emotion] += 1
    
    # 生成词云
    output_dir = "wordcloud_output"
    os.makedirs(output_dir, exist_ok=True)
    
    generate_wordcloud(all_text, f"{output_dir}/formative_experiences_wordcloud.png", 
                       "形成性经历词云图", font_path=font_path)
    if events_text:
        generate_wordcloud(events_text, f"{output_dir}/formative_events_wordcloud.png", 
                          "形成性事件词云图", font_path=font_path)
    if impacts_text:
        generate_wordcloud(impacts_text, f"{output_dir}/formative_impacts_wordcloud.png", 
                          "形成性影响词云图", font_path=font_path)
    
    return {
        'total_experiences': count,
        'word_frequency': word_freq,
        'events_analysis': {
            'word_frequency': events_word_freq,
        },
        'impacts_analysis': {
            'word_frequency': impacts_word_freq,
            'emotion_counts': emotion_counts  # 新增：情感分析
        },
        'age_mentions': {
            'ages': ages,
            'average_age': avg_age
        },
        'wordcloud_paths': {
            'all': f"{output_dir}/formative_experiences_wordcloud.png",
            'events': f"{output_dir}/formative_events_wordcloud.png" if events_text else None,
            'impacts': f"{output_dir}/formative_impacts_wordcloud.png" if impacts_text else None
        }
    }

def analyze_interests_values(data: List[Dict], font_path: str = DEFAULT_FONT_PATH) -> Dict:
    """
    分析InterestsAndValues字段
    
    Args:
        data: JSON数据列表
        font_path: 字体文件路径
        
    Returns:
        Dict: 分析结果
    """
    # 提取所有InterestsAndValues字段值
    all_iv = []
    for item in data:
        if 'InterestsAndValues' in item:
            all_iv.append(item['InterestsAndValues'])
    
    # 分别提取兴趣和价值观
    interests = []
    values = []
    
    for iv in all_iv:
        if isinstance(iv, dict):
            if 'Interests' in iv:
                interests.extend(iv['Interests'])
            if 'Values' in iv:
                values.extend(iv['Values'])
    
    # 统计数量
    interests_count = len(interests)
    values_count = len(values)
    
    # 将内容合并成文本字符串
    interests_text = normalize_field_value(interests)
    values_text = normalize_field_value(values)
    all_text = interests_text + " " + values_text
    
    # 词频分析
    word_freq = analyze_word_frequency(all_text, 30)
    interests_word_freq = analyze_word_frequency(interests_text, 20)
    values_word_freq = analyze_word_frequency(values_text, 20)
    
    # 分析兴趣-价值观共享词汇
    interests_words = set(word for word, _ in interests_word_freq)
    values_words = set(word for word, _ in values_word_freq)
    shared_words = interests_words.intersection(values_words)
    
    # 生成词云
    output_dir = "wordcloud_output"
    os.makedirs(output_dir, exist_ok=True)
    
    generate_wordcloud(all_text, f"{output_dir}/interests_values_wordcloud.png", 
                       "兴趣与价值观词云图", font_path=font_path)
    if interests_text:
        generate_wordcloud(interests_text, f"{output_dir}/interests_wordcloud.png", 
                          "兴趣词云图", font_path=font_path)
    if values_text:
        generate_wordcloud(values_text, f"{output_dir}/values_wordcloud.png", 
                          "价值观词云图", font_path=font_path)
    
    # 对兴趣进行分类
    interest_categories = {
        '艺术': ['音乐', '绘画', '舞蹈', '摄影', '电影', '戏剧', '文学', '设计', '手工', '创作'],
        '体育': ['运动', '篮球', '足球', '游泳', '健身', '瑜伽', '跑步', '骑行', '登山', '武术'],
        '科技': ['编程', '计算机', '科技', '电子', '机器人', '人工智能', '网络', '编码', '软件', '硬件'],
        '学术': ['研究', '阅读', '学习', '科学', '历史', '哲学', '数学', '语言', '文化', '教育'],
        '社交': ['交友', '社交', '聚会', '志愿者', '社区', '沟通', '团队', '合作', '领导', '组织'],
        '休闲': ['旅行', '烹饪', '园艺', '宠物', '收藏', '游戏', '电视', '动漫', '时尚', '购物']
    }
    
    # 统计兴趣分类
    interest_category_counts = {category: 0 for category in interest_categories}
    for word in jieba.cut(interests_text):
        for category, keywords in interest_categories.items():
            if any(keyword in word for keyword in keywords):
                interest_category_counts[category] += 1
    
    return {
        'interests_count': interests_count,
        'values_count': values_count,
        'word_frequency': word_freq,
        'interests_analysis': {
            'word_frequency': interests_word_freq,
            'categories': interest_category_counts  # 新增：兴趣分类统计
        },
        'values_analysis': {
            'word_frequency': values_word_freq,
        },
        'correlation': {
            'shared_words': list(shared_words),
            'correlation_score': len(shared_words) / (len(interests_words) + len(values_words)) if (len(interests_words) + len(values_words)) > 0 else 0
        },
        'wordcloud_paths': {
            'all': f"{output_dir}/interests_values_wordcloud.png",
            'interests': f"{output_dir}/interests_wordcloud.png" if interests_text else None,
            'values': f"{output_dir}/values_wordcloud.png" if values_text else None
        }
    }

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='分析JSON文件中的特定字段')
    parser.add_argument('file_path', help='JSON文件路径')
    parser.add_argument('--fields', nargs='+', 
                        choices=['StrengthsAndResources', 'SocialSupportSystem', 
                                'FormativeExperiences', 'InterestsAndValues', 'all'],
                        default=['all'],
                        help='要分析的字段，可选多个')
    parser.add_argument('--font', default=DEFAULT_FONT_PATH,
                        help='指定中文字体文件路径')
    args = parser.parse_args()
    
    # 验证字体文件存在
    font_path = args.font
    if not os.path.exists(font_path):
        print(f"警告：找不到指定的字体文件 {font_path}，可能导致中文显示异常")
    else:
        print(f"使用字体文件: {font_path}")
    
    # 加载数据
    data = load_json_data(args.file_path)
    if not data:
        return
    
    print(f"已加载 {len(data)} 条数据记录")
    
    # 确定要分析的字段
    fields_to_analyze = []
    if 'all' in args.fields:
        fields_to_analyze = ['StrengthsAndResources', 'SocialSupportSystem', 
                            'FormativeExperiences', 'InterestsAndValues']
    else:
        fields_to_analyze = args.fields
    
    # 分析各字段
    for field in fields_to_analyze:
        print(f"\n{'='*50}")
        print(f"分析字段: {field}")
        print(f"{'='*50}")
        
        if field == 'StrengthsAndResources':
            results = analyze_strengths_resources(data, font_path=font_path)
            
            print(f"总项目数: {results['total_items']}")
            print(f"平均字数: {results['average_length']:.2f}")
            
            print("\n词频统计:")
            for word, count in results['word_frequency']:
                print(f"  {word}: {count}")
            
            print(f"\n词云图已生成: {results['wordcloud_path']}")
        
        elif field == 'SocialSupportSystem':
            results = analyze_social_support(data, font_path=font_path)
            
            print(f"支持系统总数: {results['total_systems']}")
            print(f"平均支持者数量: {results['average_supporters']:.2f}")
            
            print("\n支持者类型统计:")
            for support_type, count in results['support_types'].items():
                print(f"  {support_type}: {count}")
            
            print("\n支持者角色统计:")  # 新增：输出角色统计
            for role, count in results['supporter_roles']:
                print(f"  {role}: {count}")
            
            print("\n词频统计:")
            for word, count in results['word_frequency']:
                print(f"  {word}: {count}")
            
            print(f"\n词云图已生成: {results['wordcloud_path']}")
        
        elif field == 'FormativeExperiences':
            results = analyze_formative_experiences(data, font_path=font_path)
            
            print(f"形成性经历总数: {results['total_experiences']}")
            
            print("\n词频统计:")
            for word, count in results['word_frequency']:
                print(f"  {word}: {count}")
            
            print("\n事件分析词频:")
            for word, count in results['events_analysis']['word_frequency']:
                print(f"  {word}: {count}")
            
            print("\n影响分析词频:")
            for word, count in results['impacts_analysis']['word_frequency']:
                print(f"  {word}: {count}")
            
            # 新增：输出情感分析结果
            print("\n影响情感分析:")
            emotion_counts = results['impacts_analysis']['emotion_counts']
            print(f"  积极情感词出现: {emotion_counts['积极']} 次")
            print(f"  消极情感词出现: {emotion_counts['消极']} 次")
            
            if results['age_mentions']['average_age']:
                print(f"\n年龄提及分析:")
                print(f"  提及的年龄: {results['age_mentions']['ages']}")
                print(f"  平均年龄: {results['age_mentions']['average_age']:.1f}")
            
            print("\n词云图已生成:")
            for cloud_type, path in results['wordcloud_paths'].items():
                if path:
                    print(f"  {cloud_type}: {path}")
        
        elif field == 'InterestsAndValues':
            results = analyze_interests_values(data, font_path=font_path)
            
            print(f"兴趣总数: {results['interests_count']}")
            print(f"价值观总数: {results['values_count']}")
            
            print("\n词频统计:")
            for word, count in results['word_frequency']:
                print(f"  {word}: {count}")
            
            print("\n兴趣词频:")
            for word, count in results['interests_analysis']['word_frequency']:
                print(f"  {word}: {count}")
            
            # 新增：输出兴趣分类统计
            print("\n兴趣分类:")
            for category, count in results['interests_analysis']['categories'].items():
                print(f"  {category}: {count}")
            
            print("\n价值观词频:")
            for word, count in results['values_analysis']['word_frequency']:
                print(f"  {word}: {count}")
                
            print("\n兴趣-价值观共享词汇:")
            print(f"  {', '.join(results['correlation']['shared_words'])}")
            print(f"  关联度得分: {results['correlation']['correlation_score']:.4f}")
            
            print("\n词云图已生成:")
            for cloud_type, path in results['wordcloud_paths'].items():
                if path:
                    print(f"  {cloud_type}: {path}")

if __name__ == "__main__":
    main()