from openai import OpenAI
import json
import random
import os
import datetime
import argparse
import sys
import time
from tqdm import tqdm
import concurrent.futures  # 新增并行处理库
import threading  # 新增线程安全支持

from prompts import *

# 模型配置：{模型名: 权重}
MODELS = {
    "doubao-seed-1-6-250615": 0.6,
    "deepseek-r1-250528": 0.4,
}

# Prompt配置：{prompt变量名: 权重}
PROMPTS = {
    expand_prompt: 0.6,
    expand_prompt_sim: 0.4
}

INPUT_FILE = "generated_personas_0716.json"
OUTPUT_FILE = "expand_test.json"
MAX_RETRIES = 10
FAILED_FILE = "error_personas.txt"
# "deepseek-v3-250324""gpt-4.1","doubao-seed-1-6-250615""deepseek-r1-250528","claude-sonnet-4-20250514" "doubao-1-5-pro-32k-character-250228"
# parser.add_argument('n', type=int, help='选择要处理的角色卡片数量')
# parser.add_argument('--workers', type=int, default=5, help='并行工作线程数，默认为5')
# parser.add_argument('--max_retries', type=int, default=3, help='处理失败时的最大重试次数，默认为3')


def select_random_model():
    """根据权重随机选择模型"""
    models = list(MODELS.keys())
    weights = list(MODELS.values())
    return random.choices(models, weights=weights)[0]

def select_random_prompt():
    """根据权重随机选择prompt"""
    prompts = list(PROMPTS.keys())
    weights = list(PROMPTS.values())
    return random.choices(prompts, weights=weights)[0]

def get_response(user_message, system_prompt, model, temperature=0.95, token_record=None):
    """
    获取对话生成的响应
    :param user_message: 用户消息
    :param system_prompt: 系统提示
    :param model: 模型名称
    :param temperature: 温度参数
    :param token_record: token使用记录字典
    :return: 生成的响应文本
    """
    
    # # 指定url和key
    # api_key = "sk-RMerNTL9uP4lmHh_1rwUHJvcaqKXXzb7IrAtjWTa5Ln_aFMFTNPkuzIi7vw"
    # base_url = "http://43.153.114.96:3000/v1"
    
    base_url = "https://ark.cn-beijing.volces.com/api/v3"
    api_key = "5ac78442-9d9a-4e85-8ae5-fca8086daf43"
    
    client = OpenAI(
    base_url=base_url,
    api_key=api_key)
    
    try:
        response = client.chat.completions.create(
            messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
            model=model,
            temperature=temperature,
            response_format={
                'type': 'json_object'
            },
            stream=False
        )
        
        # 记录token使用情况
        if token_record is not None and hasattr(response, 'usage'):
            prompt_tokens = response.usage.prompt_tokens
            completion_tokens = response.usage.completion_tokens
            
            token_record['prompt_tokens'] = token_record.get('prompt_tokens', 0) + prompt_tokens
            token_record['completion_tokens'] = token_record.get('completion_tokens', 0) + completion_tokens
            token_record['total_calls'] = token_record.get('total_calls', 0) + 1
        
        return response.choices[0].message.content
    except Exception as e:
        if "429" in str(e):
            print(f"速率限制超出 (429)，等待5秒后重试...")
            time.sleep(5)
            return get_response(user_message, system_prompt, model, temperature, token_record)
        else:
            print(f"API调用错误: {e}")
            time.sleep(3)
            return get_response(user_message, system_prompt, model, temperature, token_record)


def append_to_output_file(persona_data, output_file, lock=None):
    """
    实时追加单条样例到输出文件
    :param persona_data: 单条角色卡片数据
    :param output_file: 输出文件名
    :param lock: 线程锁
    """
    if lock:
        lock.acquire()

    try:
        with open(output_file, 'a', encoding='utf-8') as f:
            json.dump(persona_data, f, ensure_ascii=False, indent=2)
            f.write('\n')
        result = True
    except Exception as e:
        print(f"保存到文件失败: {e}")
        result = False
    finally:
        if lock:
            lock.release()
    return result

def append_to_error_file(persona_id, error_file, lock=None):
    """
    实时追加失败的persona_id到错误文件
    :param persona_id: 失败的角色ID
    :param error_file: 错误文件名
    :param lock: 线程锁
    """
    if lock:
        lock.acquire()

    try:
        with open(error_file, 'a', encoding='utf-8') as f:
            f.write(f"{persona_id}\n")
        result = True
    except Exception as e:
        print(f"保存错误记录失败: {e}")
        result = False
    finally:
        if lock:
            lock.release()
    return result

# 新增：处理单个角色卡片的并行函数
def process_persona(idx, persona, output_file, error_file, lock, token_records=None, max_retries=10):
    """
    并行处理单个角色卡片的函数，失败时会重试
    :param idx: 角色在列表中的索引
    :param persona: 角色卡片数据
    :param output_file: 输出文件路径
    :param error_file: 错误文件路径
    :param lock: 线程锁
    :param token_records: 各模型token使用记录字典
    :param max_retries: 最大重试次数
    :return: (idx, success)
    """
    persona_id = persona.get('persona_id', '未知')
    retry_count = 0

    while retry_count <= max_retries:  # 允许初始尝试 + max_retries次重试
        try:
            # 随机选择模型和prompt
            selected_model = select_random_model()
            selected_prompt = select_random_prompt()

            # 将角色卡片转换为字符串作为user_message
            user_message = json.dumps(persona, ensure_ascii=False, indent=2)

            # 获取对应模型的token记录
            model_token_record = token_records.get(selected_model) if token_records else None

            # 调用get_response获取扩展字段
            response_text = get_response(user_message, selected_prompt, selected_model, token_record=model_token_record)

            # 处理可能被代码块标记包裹的内容
            if response_text.startswith("```") and response_text.endswith("```"):
                response_text = response_text.strip("`")
                # 移除可能的语言标识符(如```json)
                lines = response_text.split("\n")
                if len(lines) > 1 and not lines[0].strip():
                    response_text = "\n".join(lines[1:])
                elif len(lines) > 1 and lines[0].strip().lower() in ["json", "javascript"]:
                    response_text = "\n".join(lines[1:])

            # 解析响应
            try:
                expanded_fields = json.loads(response_text)

                # 创建扩展后的角色卡片
                expanded_persona = persona.copy()
                expanded_persona.update(expanded_fields)

                # 实时写入到输出文件
                append_to_output_file(expanded_persona, output_file, lock)

                print(f"\n角色卡片 {idx+1}, ID: {persona_id} 处理成功")
                return idx, True

            except json.JSONDecodeError as e:
                retry_count += 1
                if retry_count <= max_retries:
                    wait_time = retry_count * 2  # 递增等待时间
                    print(f"\n解析响应失败, ID: {persona_id}: {e}，第{retry_count}次重试，等待{wait_time}秒...")
                    print(f"原始响应: {response_text}")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"\n解析响应失败, ID: {persona_id}: {e}，已达到最大重试次数{max_retries}")
                    print(f"原始响应: {response_text}")
                    # 记录失败的persona_id到错误文件
                    append_to_error_file(persona_id, error_file, lock)
                    return idx, False

        except Exception as e:
            retry_count += 1
            if retry_count <= max_retries:
                wait_time = retry_count * 2  # 递增等待时间
                print(f"\n处理角色卡片失败, ID: {persona_id}: {e}，第{retry_count}次重试，等待{wait_time}秒...")
                time.sleep(wait_time)
                continue
            else:
                print(f"\n处理角色卡片失败, ID: {persona_id}: {e}，已达到最大重试次数{max_retries}")
                # 记录失败的persona_id到错误文件
                append_to_error_file(persona_id, error_file, lock)
                return idx, False

def main():
    """
    主函数：处理命令行参数，读取personas.json，调用大模型扩展字段，并输出结果到personas_expand.json
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='扩展角色卡片字段')
    parser.add_argument('n', type=int, help='选择要处理的角色卡片数量')
    parser.add_argument('--output', default=OUTPUT_FILE, help='输出文件名，默认为personas_expand.json')
    parser.add_argument('--error', default=FAILED_FILE, help='错误文件名，默认为error_personas.txt')
    parser.add_argument('--workers', type=int, default=5, help='并行工作线程数，默认为5')
    parser.add_argument('--max_retries', type=int, default=MAX_RETRIES, help='处理失败时的最大重试次数，默认为3')
    args = parser.parse_args()

    # 创建token记录目录
    token_record_dir = "token_record"
    if not os.path.exists(token_record_dir):
        os.makedirs(token_record_dir)

    # 为每个模型创建token记录字典
    token_records = {}
    for model in MODELS.keys():
        token_records[model] = {
            'model': model,
            'prompt_tokens': 0,
            'completion_tokens': 0,
            'total_calls': 0,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    # 读取personas.json
    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            personas = json.load(f)
    except Exception as e:
        print(f"读取personas.json失败: {e}")
        sys.exit(1)
    
    # 检查n是否有效
    if args.n <= 0 or args.n > len(personas):
        print(f"n的值无效，必须在1到{len(personas)}之间")
        sys.exit(1)
    
    # 获取要处理的角色卡片
    selected_personas = personas[:args.n]

    # 清空输出文件和错误文件
    with open(args.output, 'w', encoding='utf-8') as f:
        pass  # 清空文件
    with open(args.error, 'w', encoding='utf-8') as f:
        pass  # 清空文件

    # 创建线程锁以确保线程安全
    lock = threading.Lock()

    print(f"开始并行处理{args.n}个角色卡片...")

    # 使用线程池进行并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=args.workers) as executor:
        # 准备任务列表
        futures = []
        for i, persona in enumerate(selected_personas):
            futures.append(
                executor.submit(
                    process_persona,
                    i,
                    persona,
                    args.output,
                    args.error,
                    lock,
                    token_records,
                    args.max_retries
                )
            )

        # 使用tqdm显示进度
        successful_count = 0
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures), desc="并行处理角色卡片"):
            try:
                _, success = future.result()
                if success:
                    successful_count += 1
            except Exception as e:
                print(f"\n处理角色卡片时发生未知错误: {e}")

        print(f"\n所有角色卡片处理完成，结果已实时写入文件: {args.output}")
        print(f"错误记录已写入文件: {args.error}")
    
    # 记录结束时间并保存token使用记录
    end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    # 为每个模型添加结束时间和统计信息
    for model, record in token_records.items():
        record['end_time'] = end_time
        record['successful_personas'] = successful_count
        record['total_personas'] = args.n

    # 保存各模型的token记录到文件
    token_record_file = os.path.join(token_record_dir, f"token_record_{timestamp}.json")
    try:
        with open(token_record_file, 'w', encoding='utf-8') as f:
            json.dump(token_records, f, ensure_ascii=False, indent=2)
        print(f"\nToken使用记录已保存到: {token_record_file}")

        # 打印各模型的token使用统计
        total_prompt_tokens = sum(record['prompt_tokens'] for record in token_records.values())
        total_completion_tokens = sum(record['completion_tokens'] for record in token_records.values())
        print(f"总输入tokens: {total_prompt_tokens}, 总输出tokens: {total_completion_tokens}")

        for model, record in token_records.items():
            if record['total_calls'] > 0:
                print(f"  {model}: 调用{record['total_calls']}次, 输入{record['prompt_tokens']}, 输出{record['completion_tokens']}")
    except Exception as e:
        print(f"\n保存Token记录失败: {e}")

    print(f"\n成功完成！共处理 {args.n} 个角色卡片，成功 {successful_count} 个")

if __name__ == "__main__":
    main()